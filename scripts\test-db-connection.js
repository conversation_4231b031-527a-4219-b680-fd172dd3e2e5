#!/usr/bin/env node

/**
 * Database Connection Test Script
 * 
 * This script tests the database connection using the current environment variables.
 * Run this script to verify your database configuration before deployment.
 * 
 * Usage:
 *   node scripts/test-db-connection.js
 */

const { PrismaClient } = require('@prisma/client')

async function testDatabaseConnection() {
  console.log('🔍 Testing database connection...')
  console.log('📊 Environment:', process.env.NODE_ENV || 'development')
  console.log('🔗 Database URL:', process.env.DATABASE_URL ? 'Set ✅' : 'Missing ❌')
  console.log('🔗 Direct URL:', process.env.DIRECT_URL ? 'Set ✅' : 'Missing ❌')
  
  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL environment variable is not set')
    process.exit(1)
  }

  // Extract connection details for logging (without password)
  const dbUrl = new URL(process.env.DATABASE_URL)
  console.log('🏠 Host:', dbUrl.hostname)
  console.log('🚪 Port:', dbUrl.port)
  console.log('🗄️  Database:', dbUrl.pathname.slice(1))
  console.log('⚙️  Search Params:', dbUrl.searchParams.toString())

  const prisma = new PrismaClient({
    log: ['error', 'warn'],
  })

  try {
    console.log('\n⏳ Attempting to connect to database...')
    
    // Test basic connection
    await prisma.$connect()
    console.log('✅ Database connection successful!')

    // Test a simple query
    console.log('⏳ Testing database query...')
    const result = await prisma.$queryRaw`SELECT 1 as test`
    console.log('✅ Database query successful:', result)

    // Test if our tables exist
    console.log('⏳ Checking if tables exist...')
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_type = 'BASE TABLE'
      ORDER BY table_name
    `
    
    if (tables.length > 0) {
      console.log('✅ Found tables:', tables.map(t => t.table_name).join(', '))
    } else {
      console.log('⚠️  No tables found. You may need to run migrations.')
    }

  } catch (error) {
    console.error('❌ Database connection failed:')
    console.error('Error:', error.message)
    
    if (error.message.includes('5432')) {
      console.log('\n💡 Suggestion: You might be using direct connection (port 5432) in production.')
      console.log('   For Vercel deployment, use connection pooling (port 6543):')
      console.log('   DATABASE_URL="********************************/db?pgbouncer=true&connection_limit=1"')
    }
    
    if (error.message.includes('6543')) {
      console.log('\n💡 Suggestion: Connection pooling port (6543) might not be accessible.')
      console.log('   For local development, you can use direct connection (port 5432):')
      console.log('   DATABASE_URL="********************************/db"')
    }
    
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }

  console.log('\n🎉 All database tests passed!')
}

// Run the test
testDatabaseConnection().catch((error) => {
  console.error('💥 Unexpected error:', error)
  process.exit(1)
})
