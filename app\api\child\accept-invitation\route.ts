import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { isDemoMode } from '@/app/config/demoMode';
import jwt from 'jsonwebtoken';

interface InvitationData {
  masterEmail: string;
  childEmail: string;
  masterId: string;
  type: string;
}

interface ChildUserData {
  email: string;
  name: string;
  role: 'child';
  zerodhaAccessToken: string;
  zerodhaRefreshToken: string;
  zerodhaUserId: string;
  masterId: string;
  masterEmail: string;
  invitationToken: string;
}

export async function POST(request: NextRequest) {
  try {
    const { token, childUserData } = await request.json();

    if (!token || !childUserData) {
      return NextResponse.json(
        { error: 'Token and child user data are required' },
        { status: 400 }
      );
    }

    // Verify the invitation token
    const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
    let invitationData: InvitationData;
    
    try {
      invitationData = jwt.verify(token, JWT_SECRET) as InvitationData;
      
      if (!invitationData || invitationData.type !== 'invitation') {
        throw new Error('Invalid token format');
      }
    } catch (err) {
      return NextResponse.json(
        { error: 'Invalid or expired invitation token' },
        { status: 400 }
      );
    }

    // Validate that the child email matches the invitation
    if (invitationData.childEmail !== childUserData.email) {
      return NextResponse.json(
        { error: 'Child email does not match invitation' },
        { status: 400 }
      );
    }

    const supabase = await createClient();
    const demoMode = isDemoMode();

    try {
      // Check if master user exists
      const { data: masterUser, error: masterError } = await supabase
        .from('users')
        .select('*')
        .eq('id', invitationData.masterId)
        .single();

      if (masterError || !masterUser) {
        return NextResponse.json(
          { error: 'Master user not found' },
          { status: 404 }
        );
      }

      // Check if child user already exists
      let childUser;
      const { data: existingChild, error: childCheckError } = await supabase
        .from('users')
        .select('*')
        .eq('email', childUserData.email)
        .single();

      if (existingChild) {
        // Update existing child user
        const { data: updatedChild, error: updateError } = await supabase
          .from('users')
          .update({
            name: childUserData.name,
            role: 'child',
            zerodha_access_token: childUserData.zerodhaAccessToken,
            zerodha_refresh_token: childUserData.zerodhaRefreshToken,
            zerodha_user_id: childUserData.zerodhaUserId,
            updated_at: new Date().toISOString()
          })
          .eq('email', childUserData.email)
          .select()
          .single();

        if (updateError) {
          console.error('Error updating child user:', updateError);
          return NextResponse.json(
            { error: 'Failed to update child user' },
            { status: 500 }
          );
        }

        childUser = updatedChild;
      } else {
        // Create new child user
        const { data: newChild, error: createError } = await supabase
          .from('users')
          .insert({
            email: childUserData.email,
            name: childUserData.name,
            role: 'child',
            zerodha_access_token: childUserData.zerodhaAccessToken,
            zerodha_refresh_token: childUserData.zerodhaRefreshToken,
            zerodha_user_id: childUserData.zerodhaUserId,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single();

        if (createError) {
          console.error('Error creating child user:', createError);
          return NextResponse.json(
            { error: 'Failed to create child user' },
            { status: 500 }
          );
        }

        childUser = newChild;
      }

      // Create or update master-child relationship
      const { data: existingRelation, error: relationCheckError } = await supabase
        .from('master_child_relations')
        .select('*')
        .eq('master_id', invitationData.masterId)
        .eq('child_id', childUser.id)
        .single();

      if (existingRelation) {
        // Update existing relationship
        const { error: updateRelationError } = await supabase
          .from('master_child_relations')
          .update({
            status: 'active',
            connected_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('master_id', invitationData.masterId)
          .eq('child_id', childUser.id);

        if (updateRelationError) {
          console.error('Error updating master-child relationship:', updateRelationError);
          return NextResponse.json(
            { error: 'Failed to update master-child relationship' },
            { status: 500 }
          );
        }
      } else {
        // Create new relationship
        const { error: createRelationError } = await supabase
          .from('master_child_relations')
          .insert({
            master_id: invitationData.masterId,
            child_id: childUser.id,
            status: 'active',
            connected_at: new Date().toISOString(),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (createRelationError) {
          console.error('Error creating master-child relationship:', createRelationError);
          return NextResponse.json(
            { error: 'Failed to create master-child relationship' },
            { status: 500 }
          );
        }
      }

      // Update invitation status to accepted
      const { error: invitationUpdateError } = await supabase
        .from('invitations')
        .update({
          status: 'accepted',
          accepted_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('token', token);

      if (invitationUpdateError) {
        console.error('Error updating invitation status:', invitationUpdateError);
        // Don't fail the request for this, just log it
      }

      console.log(`[${demoMode ? 'DEMO' : 'PROD'} MODE] Child user ${childUserData.email} successfully connected to master ${invitationData.masterEmail}`);

      return NextResponse.json({
        success: true,
        message: `Successfully connected child user to master${demoMode ? ' (Demo Mode)' : ''}`,
        childUser: {
          id: childUser.id,
          email: childUser.email,
          name: childUser.name,
          role: childUser.role
        },
        masterUser: {
          id: masterUser.id,
          email: masterUser.email,
          name: masterUser.name
        },
        demoMode
      });

    } catch (dbError) {
      console.error('Database error:', dbError);
      return NextResponse.json(
        { error: 'Database operation failed' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error accepting invitation:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
